#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <ros/ros.h>
#include <message_filters/subscriber.h>
#include <message_filters/synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Bool.h>
#include <nav_msgs/Path.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/TwistStamped.h>
#include <geometry_msgs/Twist.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/Joy.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <local_planner/NavigationResult.h>
#include <local_planner/NavigationTarget.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>

using namespace std;

#define pi 3.1415926535
//定义一个PID控制器
class PIDController
{
	public:
	PIDController(double kp, double ki, double kd)
		:
		kp_(kp),
		ki_(ki),
		kd_(kd),
		prev_error_(0),
		integral_(0)
	{
	}
	void Parameter(double Kp, double Ki, double Kd)
	{
		kp_=Kp;
		ki_=Ki;
		kd_=Kd;
	}
	//类内函数，计算PID控制量，输入分别误差，单位时间，输出限幅
	double Control(double error, double dt, double actual, double err_max, double limit)
	{
	#if 0
		double result = 0;
		integral_ += error * 0.01;
		double derivative = (error - prev_error_) / 0.01;
		prev_error_ = error;
		result = kp_ * error + ki_ * integral_  + kd_ * derivative;
		//对输出控制量大小限幅
		if (result > limit) result = limit;
		else if(result <- limit) result = -limit;
		if(fabs(result) < 0.001) result = 0;
		return result;
	#else
		// 误差限幅
		error = std::max(std::min(error, err_max), -err_max);
		double result = 0;
		integral_ += error * 0.01;
		double derivative = (error - prev_error_) / 0.01;
		prev_error_ = error;
			// 积分限幅，防止积分饱和
		double integral_limit = limit / ki_;
		integral_ = std::max(std::min(integral_, integral_limit), -integral_limit);
		result = kp_ * error + ki_ * integral_  + kd_ * derivative;
		// 输出限幅
		result = std::max(std::min(result, limit), -limit);

		// 死区处理
		const double deadzone = 0.001;
		if(fabs(result) < deadzone) {
			result = 0;
		}
		return result;
	 #endif
	}
	//类内变量
	private:
	double kp_;
	double ki_;
	double kd_;
	double prev_error_;
	double integral_;
};
//定义机器人位姿状态变量
float vehicleX = 0, vehicleY = 0, vehicleZ = 0;
float vehicleRoll = 0, vehiclePitch = 0, vehicleYaw = 0;
//目标变量
double goalX = 0, goalY = 0, goalZ = 0, goalYaw = 0;
int nav_start = 0, id = 0;
//定义各PID参数
double p_vel_yaw, i_vel_yaw, d_vel_yaw;
double p_vel_x, i_vel_x, d_vel_x;
double p_vel_y, i_vel_y, d_vel_y;
double errorYaw_max, errorX_max, errorY_max,errorYaw_min, set_yaw_precision, set_x_precision, set_y_precision;
double use_errorYaw;
double last_angular_z = 0, last_linear_x = 0, last_linear_y=0;
double Yaw_max, X_max, Y_max;
std_msgs::Bool adjustmode;
std_msgs::Bool posecalibration;
double start_time, end_time, start;
bool newTerrainCloud = false;
double vehicleLength = 1.0;
double vehicleWidth = 0.5;
double odometryTime = 0;
int arrived = 1;


pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop(new pcl::PointCloud<pcl::PointXYZI>());
//PID控制器参数初始化
PIDController pid_yaw(p_vel_yaw, i_vel_yaw, d_vel_yaw);
PIDController pid_x(p_vel_x, i_vel_x, d_vel_x);
PIDController pid_y(p_vel_y, i_vel_y, d_vel_y);
void odomHandler(const nav_msgs::Odometry::ConstPtr& odomIn)//载体当前位姿信息回调函数
{	
	//计算横滚、俯仰和偏航角
	double roll, pitch, yaw;
	geometry_msgs::Quaternion geoQuat = odomIn->pose.pose.orientation;
	tf::Matrix3x3(tf::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);
	vehicleRoll = roll;
	vehiclePitch = pitch;
	vehicleYaw = yaw;
	vehicleX = odomIn->pose.pose.position.x;
	vehicleY = odomIn->pose.pose.position.y;
	vehicleZ = odomIn->pose.pose.position.z;
	odometryTime = pcl::getTime();
}

void goalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//终点位姿信息以及导航参数回调函数
{
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
    //goalZ = goal->pose.position.z;
	tf::Quaternion quat;
    tf::quaternionMsgToTF(goal->pose.orientation, quat);
    double roll, pitch, yaw;//定义存储r\p\y的容器
    tf::Matrix3x3(quat).getRPY(roll, pitch, yaw);//进行转换
    goalYaw = yaw;
    //nav_start = goal->nav_mode;
	nav_start = true;
    //id = goal->point_id;	
	arrived = 0;
	posecalibration.data = false;
}	
void webgoalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//终点位姿信息以及导航参数回调函数
{
    goalX = goal->pose.position.x;
    goalY = goal->pose.position.y;
	//goal.pose.position.z = target->pose.position.z;
	std::cout << "web targt yaw:" << goal->pose.orientation.z << ", unit:deg" << std::endl;
	// goal.pose.orientation = tf::createQuaternionMsgFromYaw(goal->pose.orientation.z);
    //goalZ = goal->pose.position.z;
	
    goalYaw = (goal->pose.orientation.z * M_PI) / 180;
    //nav_start = goal->nav_mode;
	nav_start = true;
    //id = goal->point_id;	
	arrived = 0;
	posecalibration.data = false;
}	

void modeHandler(const std_msgs::Bool::ConstPtr& mode)//粗调精调模式回调函数
{
  adjustmode.data = mode->data;
  start = pcl::getTime();
}

void terrainCloudHandler(const sensor_msgs::PointCloud2ConstPtr& terrainCloud2)//可通行区域点云回调函数
{
    terrainCloud->clear();
    pcl::fromROSMsg(*terrainCloud2, *terrainCloud);
    pcl::PointXYZI point;
    terrainCloudCrop->clear();
    int terrainCloudSize = terrainCloud->points.size();
    for (int i = 0; i < terrainCloudSize; i++) 
    {
      point = terrainCloud->points[i];
      if (point.intensity > 0.1)//筛选出障碍物点云
      {
        terrainCloudCrop->push_back(point);
      }
    }
    //点云信息更新
    newTerrainCloud = true;  
}

int main(int argc, char** argv)//主函数
{
	ros::init(argc, argv, "calibration");
	ros::NodeHandle nh;
	ros::NodeHandle private_nh("~");

	private_nh.param("p_vel_yaw", p_vel_yaw, 0.5);
	private_nh.param("i_vel_yaw", i_vel_yaw, 0.1);
	private_nh.param("d_vel_yaw", d_vel_yaw, 0.0);
	private_nh.param("p_vel_x", p_vel_x, 0.5);
	private_nh.param("i_vel_x", i_vel_x, 0.1);
	private_nh.param("d_vel_x", d_vel_x, 0.0);
	private_nh.param("p_vel_y", p_vel_y, 0.5);
	private_nh.param("i_vel_y", i_vel_y, 0.1);
	private_nh.param("d_vel_y", d_vel_y, 0.0);
	private_nh.param("errorYaw_max", errorYaw_max, 3.0);
	private_nh.param("errorYaw_min", errorYaw_min, 1.0);
	private_nh.param("errorX_max", errorX_max, 3.0);
	private_nh.param("errorY_max", errorY_max, 3.0);
	private_nh.param("X_max", X_max, 0.5);
	private_nh.param("Y_max", Y_max, 0.1);
	private_nh.param("Yaw_max", Yaw_max, 0.1);
	private_nh.param("set_yaw_precision", set_yaw_precision, 0.3);
	private_nh.param("set_x_precision", set_x_precision, 0.3);
	private_nh.param("set_y_precision", set_y_precision, 0.3);


	//订阅载体当前位姿信息
	ros::Subscriber subOdom = nh.subscribe<nav_msgs::Odometry> ("/state_estimation", 1, odomHandler);
	//订阅导航目标信息
	ros::Subscriber subGoal = nh.subscribe<geometry_msgs::PoseStamped> ("/move_base_simple/goal", 1, goalHandler);

	ros::Subscriber subwebGoal = nh.subscribe<geometry_msgs::PoseStamped> ("web_goal_pose", 1, webgoalHandler);
	//订阅粗调精调模式信息
	ros::Subscriber subMode = nh.subscribe<std_msgs::Bool> ("/adjustmode", 1, modeHandler);
	//订阅可通行区域点云信息
  	ros::Subscriber subTerrainCloud = nh.subscribe<sensor_msgs::PointCloud2>("/terrain_map", 5, terrainCloudHandler);
	//发布用于驱动的速度信息
	ros::Publisher pubSpeed = nh.advertise<geometry_msgs::Twist> ("/raw_cmd_vel", 1);
	//发布导航状态信息
	//ros::Publisher pubResult = nh.advertise<local_planner::NavigationResult> ("/navigation_result", 1);
	//发布arrive信息
    ros::Publisher pubStop = nh.advertise<std_msgs::Int8> ("/stop", 1 );
	//发布stop信息
	ros::Publisher innerpubStop = nh.advertise<std_msgs::Int8> ("/istop", 1 );
	//订阅粗调精调模式信息
    ros::Publisher pubMode = nh.advertise<std_msgs::Bool> ("/adjustmode", 5);
	pid_x.Parameter(p_vel_x,i_vel_x,d_vel_x);
  	pid_y.Parameter(p_vel_y,i_vel_y,d_vel_y);
  	pid_yaw.Parameter(p_vel_yaw,i_vel_yaw,d_vel_yaw);
	ros::Rate rate(100);//频率为100hz
	bool status = ros::ok();
	while (status)
	{
		ros::spinOnce();
		if (adjustmode.data && nav_start == 1 && newTerrainCloud)
		{
			newTerrainCloud = false;
			geometry_msgs::Twist cmd_vel;
			double errorX = cos(vehicleYaw) * (goalX - vehicleX) + sin(vehicleYaw) * (goalY - vehicleY);//x轴相对误差
			double errorY = -sin(vehicleYaw) * (goalX - vehicleX) + cos(vehicleYaw) * (goalY - vehicleY);//y轴相对误差
			double errorYaw = goalYaw - vehicleYaw;//偏航角相对误差
			double use_errorYaw = errorYaw;
			//将角度误差转成-180度到180度
			if (errorYaw > pi) errorYaw = errorYaw - 2 * pi;
			else if (errorYaw < -pi) errorYaw = errorYaw + 2 * pi;

			if (abs(errorX) >= set_x_precision && abs(errorY) >= set_y_precision && !posecalibration.data )
			{
				cmd_vel.linear.y = pid_y.Control(errorY,0.01,last_linear_y,errorY_max,Y_max);//pid计算Y轴线速度
				last_linear_y = cmd_vel.linear.y;
				cmd_vel.linear.x = pid_x.Control(errorX,0.01,last_linear_x,errorX_max,X_max);//pid计算X轴线速度
				last_linear_x = cmd_vel.linear.x;
				// posecalibration.data = true;

				//std::cout << "web targt yaw:" << goalYaw << ",unit:rad." << std::endl;
				std::cout<<"errorX:"<<errorX<<", errorY:"<<errorY <<std::endl;
			}
			else//先完成位置的调整，当位置差小于0.3后，开始姿态的调整
			{
				posecalibration.data = true;
				if(use_errorYaw < errorYaw_min) use_errorYaw = errorYaw_min;

				if(errorYaw >= 0)
				{
				    cmd_vel.angular.z = abs(pid_yaw.Control(use_errorYaw,0.01,last_angular_z,errorYaw_max,Yaw_max));//pid计算角速度
				}
				else if(errorYaw < 0)
				{
				    cmd_vel.angular.z = -abs(pid_yaw.Control(use_errorYaw,0.01,last_angular_z,errorYaw_max,Yaw_max));//pid计算角速度
				}
				last_angular_z = cmd_vel.angular.z;

				std::cout << "web targt yaw:" << goalYaw << ",unit:rad." << std::endl;
				std::cout<<"X:"<<vehicleX<<", Y:"<<vehicleY<<", 角度:"<<vehicleYaw<< ",unit:rad." <<std::endl;
			}

			//若角度误差在设置阈值内则发布到达信息
			if (abs(errorYaw) <= set_yaw_precision)
			{
#if 0
				//再进行位置精调
				if (abs(errorX) >= set_x_precision || abs(errorY) >= set_y_precision)
				{
					if(abs(errorX) >= set_x_precision)
					{
						cmd_vel.linear.x = pid_x.Control(errorX,0.01,last_linear_x,errorX_max,X_max);//pid计算X轴线速度
						last_linear_x = cmd_vel.linear.x;
					}
					else if(abs(errorY) >= set_y_precision)
					{
						cmd_vel.linear.y = pid_y.Control(errorY,0.01,last_linear_y,errorY_max,Y_max);//pid计算Y轴线速度
						last_linear_y = cmd_vel.linear.y;
					}
					// posecalibration.data = true;
					cmd_vel.angular.z = 0;
					std::cout<<"errorX:"<<errorX<<", errorY:"<<errorY <<std::endl;
				}
				else
#endif
				{
					std_msgs::Int8 safetystop;
					end_time = pcl::getTime();
					if (end_time - start_time >= 1 && arrived == 0)
					{
						cmd_vel.linear.x = 0;
						cmd_vel.linear.y = 0;
						cmd_vel.angular.z = 0;
						adjustmode.data = false;
						pubMode.publish(adjustmode);//关闭精调标志位
						
						
						safetystop.data = 1;
						pubStop.publish(safetystop);//发布停止标志位
						innerpubStop.publish(safetystop);//发布innerstop停止标志位
						arrived = 1;
						std::cout<<"***********已到达目标点***********"<<std::endl;
						std::cout<<"X方向误差:"<<errorX<<", Y方向误差:"<<errorY<<", 角度误差:"<<errorYaw<< ", unit:rad" <<std::endl;
						std::cout<<"X:"<<vehicleX<<", Y:"<<vehicleY<<", 角度:"<<vehicleYaw<< ", unit:rad" <<std::endl;
						
					}
				}
				
			}
			
			else
		    {
				start_time = pcl::getTime();
		    }
#if 0
			//运控有最小速度的要求：线速度最小0.1m/s，角速度最小0.15 rad/s	
			if(cmd_vel.linear.x < 0.11 && cmd_vel.linear.x > 1e-6)
			{
				cmd_vel.linear.x = 0.11;
			}
			else if(cmd_vel.linear.x > -0.11 && cmd_vel.linear.x < -0.000001)
			{
				cmd_vel.linear.x = -0.11;
			}
				
			if(cmd_vel.linear.y < 0.11 && cmd_vel.linear.y > 1e-6)
			{
				cmd_vel.linear.y = 0.11;
			}
			else if(cmd_vel.linear.y > -0.11 && cmd_vel.linear.y < -0.000001)
			{
				cmd_vel.linear.y = -0.11;
			}

			if(cmd_vel.angular.z < 0.16 && cmd_vel.angular.z > 1e-6)
			{
				cmd_vel.angular.z = 0.16;
			}
			else if(cmd_vel.angular.z > -0.16 && cmd_vel.angular.z < -0.000001)
			{
				cmd_vel.angular.z = -0.16;
			}
#endif
			pubSpeed.publish(cmd_vel);
			
		}
		status = ros::ok();
		rate.sleep();
	}
	return 0;
}
