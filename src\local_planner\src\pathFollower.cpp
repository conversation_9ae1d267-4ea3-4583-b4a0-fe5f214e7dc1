#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <ros/ros.h>
#include <message_filters/subscriber.h>
#include <message_filters/synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Bool.h>
#include <nav_msgs/Path.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/TwistStamped.h>
#include <geometry_msgs/Twist.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/Joy.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <local_planner/NavigationResult.h>
#include <local_planner/NavigationTarget.h>
#include <pcl/common/time.h>

using namespace std;

const double PI = 3.1415926;
double sensorOffsetX = 0;
double sensorOffsetY = 0;
int pubSkipNum = 1;
int pubSkipCount = 0;
bool twoWayDrive = false;
double lookAheadDis = 0.5;
double yawRateGain = 1.0;
double stopYawRateGain = 1.0;
double maxYawRate = 30.0;
double maxSpeed = 0.8;
double maxAccel = 3.0;
double switchTimeThre = 1.0;
double dirDiffThre = 0.1;
double stopDisThre = 0.2;
double slowDwnDisThre = 1.0;
bool useInclRateToSlow = false;
double inclRateThre = 120.0;
double slowRate1 = 0.25;
double slowRate2 = 0.5;
double slowTime1 = 2.0;
double slowTime2 = 2.0;
bool useInclToStop = false;
double inclThre = 45.0;
double stopTime = 5.0;
bool noRotAtGoal = true;
bool adjustmode = false;
double goalX = 0, goalY = 0, goalZ = 0;
int nav_start = 0;
double modifySpeed;
float joyYaw = 0;
float vehicleX = 0, vehicleY = 0, vehicleZ = 0;
float vehicleRoll = 0, vehiclePitch = 0, vehicleYaw = 0;
float vehicleXRec = 0, vehicleYRec = 0, vehicleZRec = 0;
float vehicleRollRec = 0, vehiclePitchRec = 0, vehicleYawRec = 0;
float vehicleYawRate = 0, vehicleSpeed = 0;
double odomTime = 0;
double slowInitTime = 0;
double stopInitTime = false;
int pathPointID = 0;
bool pathInit = false;
bool navFwd = true;
double switchTime = 0;
int safetyStop = 0;
nav_msgs::Path path;
bool rotinit = false;
double odometryTime = 0;
// 添加控制输出滤波变量
float filteredYawRate = 0;
const float yawRateFilterAlpha = 0.7; // 滤波系数，0.7表示70%保留当前值，30%保留历史值

void odomHandler(const nav_msgs::Odometry::ConstPtr& odomIn)//载体当前姿态信息回调函数
{
  odomTime = odomIn->header.stamp.toSec();
  //计算姿态角
  double roll, pitch, yaw;
  geometry_msgs::Quaternion geoQuat = odomIn->pose.pose.orientation;
  tf::Matrix3x3(tf::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);
  vehicleRoll = roll;
  vehiclePitch = pitch;
  vehicleYaw = yaw;
  vehicleX = odomIn->pose.pose.position.x - cos(yaw) * sensorOffsetX + sin(yaw) * sensorOffsetY;
  vehicleY = odomIn->pose.pose.position.y - sin(yaw) * sensorOffsetX - cos(yaw) * sensorOffsetY;
  vehicleZ = odomIn->pose.pose.position.z;
  if ((fabs(roll) > inclThre * PI / 180.0 || fabs(pitch) > inclThre * PI / 180.0) && useInclToStop)
  {
    stopInitTime = odomIn->header.stamp.toSec();
  }
  if ((fabs(odomIn->twist.twist.angular.x) > inclRateThre * PI / 180.0 || fabs(odomIn->twist.twist.angular.y) > inclRateThre * PI / 180.0) && useInclRateToSlow)
  {
    slowInitTime = odomIn->header.stamp.toSec();
  }
  odometryTime = pcl::getTime();
}

void pathHandler(const nav_msgs::Path::ConstPtr& pathIn)//局部轨迹信息回调函数
{
  int pathSize = pathIn->poses.size();
  path.poses.resize(pathSize);
  for (int i = 0; i < pathSize; i++) {
    path.poses[i].pose.position.x = pathIn->poses[i].pose.position.x;
    path.poses[i].pose.position.y = pathIn->poses[i].pose.position.y;
    path.poses[i].pose.position.z = pathIn->poses[i].pose.position.z;
  }
  //记录接收到轨迹时刻的载体位姿信息
  vehicleXRec = vehicleX;
  vehicleYRec = vehicleY;
  vehicleZRec = vehicleZ;
  vehicleRollRec = vehicleRoll;
  vehiclePitchRec = vehiclePitch;
  vehicleYawRec = vehicleYaw;
  //轨迹初始化完成
  pathPointID = 0;
  pathInit = true;
}

void modeHandler(const std_msgs::Bool::ConstPtr& mode)//粗调精调模式回调函数
{
  adjustmode = mode->data;
}

void goalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//终点目标位姿及导航参数回调函数
{
  goalX = goal->pose.position.x;
  goalY = goal->pose.position.y;
  //goalZ = goal->pose.position.z;
  nav_start = 1;
  //普通速、低速、高速切换
  /*
  if (goal->speed == 0)
  maxSpeed = 0.8;
  else if (goal->speed == 1)
  maxSpeed = 0.5;
  else if (goal->speed == 2)
  maxSpeed = 1.0;
  */
  //前进后退切换
  //if (goal->manner == 0)
  twoWayDrive = false;
  //else if (goal->manner == 1)
  //twoWayDrive = true;
  modifySpeed = maxSpeed;
  navFwd = true;
  switchTime = 0;
  rotinit = false;
  dirDiffThre = 0.1;
}

void webgoalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//终点目标位姿及导航参数回调函数
{
  goalX = goal->pose.position.x;
  goalY = goal->pose.position.y;
  //goalZ = goal->pose.position.z;
  nav_start = 1;
  //普通速、低速、高速切换
  /*
  if (goal->speed == 0)
  maxSpeed = 0.8;
  else if (goal->speed == 1)
  maxSpeed = 0.5;
  else if (goal->speed == 2)
  maxSpeed = 1.0;
  */
  //前进后退切换
  //if (goal->manner == 0)
  twoWayDrive = false;
  //else if (goal->manner == 1)
  //twoWayDrive = true;
  modifySpeed = maxSpeed;
  navFwd = true;
  switchTime = 0;
  rotinit = false;
  dirDiffThre = 0.1;
}

void stopHandler(const std_msgs::Int8::ConstPtr& stop)//停止信息回调函数
{
  safetyStop = stop->data;
}

int main(int argc, char** argv)
{
  ros::init(argc, argv, "pathFollower");
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");

  private_nh.param("sensorOffsetX", sensorOffsetX, 0.0);
  private_nh.param("sensorOffsetY", sensorOffsetY, 0.0);
  private_nh.param("pubSkipNum", pubSkipNum, 1);
  private_nh.param("lookAheadDis", lookAheadDis, 0.5);
  private_nh.param("yawRateGain", yawRateGain, 1.0);
  private_nh.param("stopYawRateGain", stopYawRateGain, 1.0);
  private_nh.param("maxYawRate", maxYawRate, 30.0);
  private_nh.param("maxAccel", maxAccel, 3.0);
  private_nh.param("switchTimeThre", switchTimeThre, 1.0);
  private_nh.param("dirDiffThre", dirDiffThre, 0.1);
  private_nh.param("stopDisThre", stopDisThre, 0.2);
  private_nh.param("slowDwnDisThre", slowDwnDisThre, 0.2);
  private_nh.param("useInclRateToSlow", useInclRateToSlow, false);
  private_nh.param("inclRateThre", inclRateThre, 120.0);
  private_nh.param("slowRate1", slowRate1, 0.25);
  private_nh.param("slowRate2", slowRate2, 0.5);
  private_nh.param("slowTime1", slowTime1, 2.0);
  private_nh.param("slowTime2", slowTime2, 2.0);
  private_nh.param("useInclToStop", useInclToStop, false);
  private_nh.param("inclThre", inclThre, 45.0);
  private_nh.param("stopTime", stopTime, 5.0);
  private_nh.param("noRotAtGoal", noRotAtGoal, true);
  //订阅载体当前位姿信息
  ros::Subscriber subOdom = nh.subscribe<nav_msgs::Odometry> ("/state_estimation", 1, odomHandler);
  //订阅局部轨迹信息
  ros::Subscriber subPath = nh.subscribe<nav_msgs::Path> ("/local_path", 5, pathHandler);
  //订阅停止信息
  ros::Subscriber subStop = nh.subscribe<std_msgs::Int8> ("/istop", 1 ,stopHandler);
  //订阅粗调精调模式信息
  ros::Subscriber subMode = nh.subscribe<std_msgs::Bool> ("/adjustmode", 1, modeHandler);
  //订阅导航目标信息
  ros::Subscriber subGoal = nh.subscribe<geometry_msgs::PoseStamped> ("/move_base_simple/goal", 1, goalHandler);
  
  ros::Subscriber subwebGoal = nh.subscribe<geometry_msgs::PoseStamped> ("/web_goal_pose", 1, webgoalHandler);
  //发布用于驱动的速度信息
  ros::Publisher pubSpeed = nh.advertise<geometry_msgs::Twist> ("/raw_cmd_vel", 1);

  geometry_msgs::Twist cmd_vel;

  ros::Rate rate(100);
  bool status = ros::ok();
  while (status) 
  {
    ros::spinOnce();
    // std::cout << "safetyStop:" <<  safetyStop <<std::endl;
    if (safetyStop == 1) 
    {

      cmd_vel.linear.x = 0;
      cmd_vel.linear.y = 0;
      cmd_vel.angular.z = 0;
      pubSpeed.publish(cmd_vel);
    }
    if (pathInit && !adjustmode && nav_start == 1 && safetyStop != 1) 
    {
      // std::cout << "safetyStop:" <<  safetyStop <<std::endl;
      //计算接收到轨迹时的位置在当前载体系下的坐标
      float vehicleXRel = cos(vehicleYawRec) * (vehicleX - vehicleXRec) + sin(vehicleYawRec) * (vehicleY - vehicleYRec);
      float vehicleYRel = -sin(vehicleYawRec) * (vehicleX - vehicleXRec) + cos(vehicleYawRec) * (vehicleY - vehicleYRec);
      //计算接收到轨迹时的位置距轨迹最后一个点的距离
      int pathSize = path.poses.size();
      float endDisX = path.poses[pathSize - 1].pose.position.x - vehicleXRel;
      float endDisY = path.poses[pathSize - 1].pose.position.y - vehicleYRel;
      float endDis = sqrt(endDisX * endDisX + endDisY * endDisY);
      //寻找前视距离范围内的最后一个点
      float disX, disY, dis;
      while (pathPointID < pathSize - 1)
      {
        disX = path.poses[pathPointID].pose.position.x - vehicleXRel;
        disY = path.poses[pathPointID].pose.position.y - vehicleYRel;
        dis = sqrt(disX * disX + disY * disY);
        if (dis < lookAheadDis) pathPointID++;
        else break;
      }
      //计算接收到轨迹时的位置距前视距离内最后一个点的距离以及角度
      disX = path.poses[pathPointID].pose.position.x - vehicleXRel;
      disY = path.poses[pathPointID].pose.position.y - vehicleYRel;
      dis = sqrt(disX * disX + disY * disY);
      float pathDir = atan2(disY, disX);
      //计算当前位置与前视距离最后一个点的夹角
      float dirDiff = vehicleYaw - vehicleYawRec - pathDir;
      if (!rotinit) dirDiff = vehicleYaw - atan2(goalY - vehicleY, goalX - vehicleX);
      //保证夹角在-pi到pi内
      if (dirDiff > PI) dirDiff -= 2 * PI;
      else if (dirDiff < -PI) dirDiff += 2 * PI;
      if (dirDiff > PI) dirDiff -= 2 * PI;
      else if (dirDiff < -PI) dirDiff += 2 * PI;
      //判断是否进行双向驱动
      if (twoWayDrive) {
        double time = ros::Time::now().toSec();
        //夹角大于90度且当前处于前向运动转换为后向运动
        if (fabs(dirDiff) > PI / 2 && navFwd && time - switchTime > switchTimeThre)
        {
          navFwd = false;
          switchTime = time;
        } 
        //夹角小于90度且当前处于后向运动转换为前向运动
        else if (fabs(dirDiff) < PI / 2 && !navFwd && time - switchTime > switchTimeThre)
        {
          navFwd = true;
          switchTime = time;
        }
      }
      //计算载体距离终点距离
      double distance = sqrt(pow(vehicleX - goalX, 2) + pow(vehicleY - goalY, 2));
      //计算载体在三米内减速到最小速度所需的加速度
      double a = (maxSpeed * maxSpeed - 0.1)/(2 * 3);
      //判断是否进入减速范围
      if (distance < 3) modifySpeed = sqrt(2 * a * distance + 0.1);
      else modifySpeed = maxSpeed;
      float joySpeed2 = modifySpeed;
      //判断前后向运动，若后向将角度设置在90度内，速度设置为负
      if (!navFwd) 
      {
        dirDiff += PI;
        if (dirDiff > PI) dirDiff -= 2 * PI;
        joySpeed2 *= -1;
      }
      //判断线速度是否为0，若为0则采用停止转角增益，若不为0则采用普通转角增益
      if (fabs(vehicleSpeed) < 2.0 * maxAccel / 100.0) vehicleYawRate = -stopYawRateGain * dirDiff;
      else vehicleYawRate = -yawRateGain * dirDiff;
      //对载体角速度进行限幅
      if (vehicleYawRate > maxYawRate * PI / 180.0) vehicleYawRate = maxYawRate * PI / 180.0;
      else if (vehicleYawRate < -maxYawRate * PI / 180.0) vehicleYawRate = -maxYawRate * PI / 180.0;
      //若局部距离小于阈值角速度为0
      if (pathSize <= 1 || (dis < stopDisThre && noRotAtGoal)) vehicleYawRate = 0;
      //若局部距离小于减速距离则进行减速
      if (pathSize <= 1) joySpeed2 = 0;
      else if (endDis < slowDwnDisThre) joySpeed2 *= endDis / slowDwnDisThre;
      //这部分未使用到
      float joySpeed3 = joySpeed2;
      if (odomTime < slowInitTime + slowTime1 && slowInitTime > 0) joySpeed3 *= slowRate1;
      else if (odomTime < slowInitTime + slowTime1 + slowTime2 && slowInitTime > 0) joySpeed3 *= slowRate2;
      //若夹角小于阈值且再停止范围外则正常加减速
      if (fabs(dirDiff) < dirDiffThre && dis > stopDisThre)
      {
        if (vehicleSpeed < joySpeed3) vehicleSpeed += maxAccel / 100.0;
        else if (vehicleSpeed > joySpeed3) vehicleSpeed -= maxAccel / 100.0;
        rotinit = true;
        dirDiffThre = 0.1;
      }
      //若夹角大于阈值，则先考虑转弯再前进
      else 
      {
        if (vehicleSpeed > 0) vehicleSpeed -= maxAccel / 100.0;
        else if (vehicleSpeed < 0) vehicleSpeed += maxAccel / 100.0;
      }
      //这部分未使用到
      if (odomTime < stopInitTime + stopTime && stopInitTime > 0) {
        vehicleSpeed = 0;
        vehicleYawRate = 0;
      }
      //改变速度输出频率
      pubSkipCount--;
      if (pubSkipCount < 0) 
      {
                //发布用于驱动的规划速度信息
        if (fabs(vehicleSpeed) <= maxAccel / 100.0) cmd_vel.linear.x = 0;
        // if (fabs(vehicleSpeed) < 0.25) cmd_vel.linear.x = 0.25;
        else cmd_vel.linear.x = vehicleSpeed;
        // if (fabs(vehicleYawRate) <= 0.25) cmd_vel.angular.z = 0.25;
        if (fabs(vehicleYawRate) <= 0.01) cmd_vel.angular.z = 0;
        else cmd_vel.angular.z = vehicleYawRate;
        //停止信息速度清零
        if (safetyStop == 1 || pcl::getTime() - odometryTime > 0.5)
        {
          cmd_vel.linear.x = 0;
          cmd_vel.angular.z = 0;
        }
        pubSpeed.publish(cmd_vel);
        pubSkipCount = pubSkipNum;
      }
    }
    status = ros::ok();
    rate.sleep();
  }
  return 0;
}
