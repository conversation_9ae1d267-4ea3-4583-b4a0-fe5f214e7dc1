# 无人车直线行驶优化方法

## 1. 路径平滑算法优化

在`rrt_star_ros.cpp`中，路径平滑相关参数和函数可以优化：

```cpp
// 当前设置
path_point_spacing_ = 0.25; // 路径点间隔
angle_difference_ = M_PI/20; // 前后相邻点向量角度差
```

### 优化建议：
- **增加平滑度**：将`angle_difference_`减小到`M_PI/30`或更小，使路径转弯更加平缓
- **优化`optimizationPath`函数**：增加迭代次数或改进平滑算法，可以考虑使用贝塞尔曲线或B样条曲线替代当前的简单平均法
- **添加曲率约束**：在`insertPointForPath`和`optimizationPath`函数中增加曲率约束，避免生成急转弯

```cpp
// 建议的优化代码示例
void RRTstarPlannerROS::optimizationPath(std::vector< std::pair<double, double> >& plan, double movement_angle_range)
{
    // 增加更多平滑迭代次数
    const int MAX_ITERATIONS = 2000; // 从1000增加到2000
    
    // 使用更严格的平滑条件
    double smooth_factor = 0.25; // 平滑因子
    
    // 其余代码逻辑...
    // 在平滑过程中可以添加曲率约束
}
```

## 2. 轨迹跟踪控制参数调整

在`pathFollower.cpp`中，需要调整控制参数：

### 优化建议：
- **增加前视距离**：将`lookAheadDis`从0.5增加到0.8-1.0，减少对小扰动的敏感性
- **降低偏航角速率增益**：将`yawRateGain`降低20-30%，减少控制输出的剧烈变化
- **添加控制输出滤波**：对控制指令进行低通滤波，平滑控制输出

```cpp
// 建议的控制参数
double lookAheadDis = 0.8;  // 从0.5增加到0.8
double yawRateGain = 0.7;   // 从1.0降低到0.7
double maxYawRate = 20.0;   // 从30.0降低到20.0

// 添加控制输出滤波
double filtered_angular_z = 0.0;
const double filter_alpha = 0.7; // 滤波系数

// 在控制循环中应用滤波
filtered_angular_z = filter_alpha * cmd_vel.angular.z + (1 - filter_alpha) * filtered_angular_z;
cmd_vel.angular.z = filtered_angular_z;
```

## 3. RRT*算法参数优化

在RRT*算法中，可以优化采样和路径生成策略：

### 优化建议：
- **增加目标导向采样概率**：将目标导向采样的概率从0.2增加到0.3-0.4，提高路径直线性
- **优化rewire过程**：在`rewire`函数中增加路径平滑度评估，优先选择更平滑的路径
- **添加路径后处理**：在生成路径后增加额外的平滑处理步骤

```cpp
// 建议修改采样策略
if(rand_nu > 7) // 从>1(0.8概率随机)改为>7(0.3概率随机)
{
    p_rand = sampleFree(); // 随机采样
}
else // 0.7概率使用目标导向采样
{
    p_rand.first = goal.pose.position.x;
    p_rand.second = goal.pose.position.y;
}
```

## 4. 速度与转向耦合优化

在`localPlanner.cpp`中，速度与路径规划的耦合可以优化：

### 优化建议：
- **动态调整路径尺度**：根据速度和转弯角度动态调整路径尺度，高速时使用更大的尺度
- **添加速度规划**：在转弯处自动降低速度，减少控制难度

```cpp
// 建议的速度与转向耦合优化
float pathScale = defPathScale;
if (pathScaleBySpeed) {
    // 根据速度和转弯角度动态调整
    float turnFactor = std::abs(joyDir) / 90.0; // 转弯因子，0-1
    pathScale = defPathScale * (maxSpeed * (1.0 - turnFactor*0.3)); // 转弯时适当减小尺度
}
if (pathScale < minPathScale) pathScale = minPathScale;

// 在转弯处降低速度
float speedFactor = 1.0;
if (std::abs(joyDir) > 30.0) {
    speedFactor = 1.0 - (std::abs(joyDir) - 30.0) / 60.0 * 0.5;
    if (speedFactor < 0.5) speedFactor = 0.5;
}
modifySpeed = maxSpeed * speedFactor;
```

## 5. 传感器数据处理优化

### 优化建议：
- **增加传感器数据滤波**：对IMU和里程计数据进行更强的滤波处理
- **优化位姿估计**：使用更先进的位姿估计算法，如扩展卡尔曼滤波
- **添加异常值检测**：检测并剔除传感器数据中的异常值

```cpp
// 建议在odometryCallback中添加滤波
void RRTstarPlannerWithCostmap::odometryCallback(const nav_msgs::Odometry::ConstPtr& odom)
{
    // 添加简单的移动平均滤波
    static std::vector<double> x_buffer(5, 0);
    static std::vector<double> y_buffer(5, 0);
    static std::vector<double> yaw_buffer(5, 0);
    static int buffer_index = 0;
    
    // 更新缓冲区
    x_buffer[buffer_index] = odom->pose.pose.position.x;
    y_buffer[buffer_index] = odom->pose.pose.position.y;
    
    // 计算yaw角
    double yaw = tf::getYaw(odom->pose.pose.orientation);
    yaw_buffer[buffer_index] = yaw;
    
    buffer_index = (buffer_index + 1) % 5;
    
    // 计算平均值
    double x_avg = 0, y_avg = 0, yaw_avg = 0;
    for(int i=0; i<5; i++) {
        x_avg += x_buffer[i];
        y_avg += y_buffer[i];
        yaw_avg += yaw_buffer[i];
    }
    x_avg /= 5;
    y_avg /= 5;
    yaw_avg /= 5;
    
    // 使用滤波后的值
    robot_pose.header = odom->header;
    robot_pose.header.frame_id = "map";
    robot_pose.pose.position.x = x_avg;
    robot_pose.pose.position.y = y_avg;
    robot_pose.pose.position.z = odom->pose.pose.position.z;
    robot_pose.pose.orientation = tf::createQuaternionMsgFromYaw(yaw_avg);
}
```

## 6. 系统集成优化

### 优化建议：
- **优化重规划策略**：减少不必要的重规划，避免路径突变
- **添加平滑过渡**：在路径切换时添加平滑过渡，避免控制指令突变
- **调整控制频率**：确保控制循环频率稳定，避免时间间隔不均匀导致的抖动

## 实施建议

1. **优先级排序**：
   - 第一优先级：路径平滑算法优化和控制参数调整
   - 第二优先级：RRT*算法参数优化和速度与转向耦合优化
   - 第三优先级：传感器数据处理优化和系统集成优化

2. **测试方法**：
   - 每次只修改一个参数或算法，进行对比测试
   - 记录修改前后的行驶轨迹和角度变化
   - 使用定量指标评估优化效果，如角度变化率、路径平滑度等

3. **注意事项**：
   - 参数调整需要根据实际车辆性能进行微调
   - 确保优化后的算法在各种场景下都能稳定工作
   - 考虑计算资源消耗，避免过度复杂的算法导致实时性下降 